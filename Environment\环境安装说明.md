# 混凝土抗压强度预测项目环境配置说明

## 系统要求
- Python 3.8 或更高版本
- 操作系统：Windows/macOS/Linux

## 环境安装步骤

### 1. 创建虚拟环境（推荐）
```bash
# 使用conda创建虚拟环境
conda create -n concrete_analysis python=3.9
conda activate concrete_analysis

# 或使用venv创建虚拟环境
python -m venv concrete_env
# Windows激活
concrete_env\Scripts\activate
# macOS/Linux激活
source concrete_env/bin/activate
```

### 2. 安装依赖库
```bash
# 方法1：使用requirements.txt安装
pip install -r requirements.txt

# 方法2：手动安装主要库
pip install pandas numpy scikit-learn matplotlib seaborn pyecharts scipy jupyter
```

### 3. 验证安装
```python
import pandas as pd
import numpy as np
import sklearn
import matplotlib.pyplot as plt
import seaborn as sns
print("所有库安装成功！")
```

## 项目文件结构
```
concrete_analysis/
├── Environment/
│   ├── requirements.txt
│   └── 环境安装说明.md
├── concrete.csv
├── concrete_analysis.py
├── results/
│   ├── figures/
│   └── models/
└── README.md
```

## 运行项目
```bash
python concrete_analysis.py
```

## 常见问题解决

### 1. 如果遇到编码问题
确保CSV文件使用UTF-8编码，或在代码中指定编码：
```python
df = pd.read_csv('concrete.csv', encoding='utf-8')
```

### 2. 如果matplotlib中文显示问题
```python
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows
plt.rcParams['axes.unicode_minus'] = False
```

### 3. 内存不足问题
如果数据集较大，可以考虑：
- 使用数据采样
- 分批处理数据
- 优化数据类型

## 联系信息
如有问题，请检查：
1. Python版本是否符合要求
2. 所有依赖库是否正确安装
3. 数据文件路径是否正确
