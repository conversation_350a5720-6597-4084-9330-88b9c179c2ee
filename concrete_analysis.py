#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
混凝土抗压强度预测项目
作者：AI助手
日期：2024年
描述：基于混凝土配比数据预测抗压强度的完整机器学习项目
"""

# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.linear_model import LinearRegression
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.cluster import KMeans
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, silhouette_score
from scipy import stats
import os

# 忽略警告信息
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ConcreteStrengthAnalysis:
    """混凝土抗压强度分析类"""
    
    def __init__(self, data_path='concrete.csv'):
        """
        初始化分析类
        
        参数:
        data_path: 数据文件路径
        """
        self.data_path = data_path
        self.df = None
        self.df_processed = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.results = {}
        
        # 创建结果保存目录
        os.makedirs('results/figures', exist_ok=True)
        os.makedirs('results/models', exist_ok=True)
        
        print("=" * 60)
        print("混凝土抗压强度预测项目初始化完成")
        print("=" * 60)
    
    def load_and_explore_data(self):
        """
        1. 数据加载与探索性分析
        """
        print("\n1. 数据加载与探索性分析")
        print("-" * 40)
        
        try:
            # 加载数据 - 尝试不同编码
            try:
                self.df = pd.read_csv(self.data_path, encoding='utf-8')
            except UnicodeDecodeError:
                try:
                    self.df = pd.read_csv(self.data_path, encoding='latin-1')
                except UnicodeDecodeError:
                    self.df = pd.read_csv(self.data_path, encoding='gbk')

            print(f"✓ 数据加载成功，数据形状: {self.df.shape}")
            
            # 设置列名（中文）
            column_names = [
                '水泥含量', '高炉矿渣', '粉煤灰', '水含量', 
                '高效减水剂', '粗骨料', '细骨料', '龄期', '抗压强度'
            ]
            self.df.columns = column_names
            
            # 基本信息
            print(f"✓ 数据维度: {self.df.shape[0]}行 × {self.df.shape[1]}列")
            print(f"✓ 特征数量: {self.df.shape[1] - 1}个")
            print(f"✓ 目标变量: 抗压强度")
            
            # 数据类型和缺失值检查
            print("\n数据基本信息:")
            print(self.df.info())
            
            # 统计描述
            print("\n数据统计描述:")
            print(self.df.describe().round(2))
            
            # 检查缺失值
            missing_values = self.df.isnull().sum()
            print(f"\n缺失值检查:")
            if missing_values.sum() == 0:
                print("✓ 无缺失值")
            else:
                print(missing_values[missing_values > 0])
            
            # 检查重复值
            duplicates = self.df.duplicated().sum()
            print(f"重复值数量: {duplicates}")
            
            return True
            
        except Exception as e:
            print(f"✗ 数据加载失败: {e}")
            return False
    
    def data_preprocessing(self):
        """
        2. 数据预处理
        """
        print("\n2. 数据预处理")
        print("-" * 40)
        
        # 复制原始数据
        self.df_processed = self.df.copy()
        
        # 异常值检测（使用IQR方法）
        print("异常值检测与处理:")
        numeric_columns = self.df_processed.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            Q1 = self.df_processed[col].quantile(0.25)
            Q3 = self.df_processed[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = ((self.df_processed[col] < lower_bound) | 
                       (self.df_processed[col] > upper_bound)).sum()
            
            if outliers > 0:
                print(f"  {col}: {outliers}个异常值")
                # 对于异常值，我们选择保留，因为在工程数据中可能是有意义的
        
        # 数据标准化对比
        print("\n数据标准化处理:")
        
        # 分离特征和目标变量
        X = self.df_processed.drop('抗压强度', axis=1)
        y = self.df_processed['抗压强度']
        
        # Min-Max标准化
        scaler_minmax = MinMaxScaler()
        X_minmax = pd.DataFrame(
            scaler_minmax.fit_transform(X), 
            columns=X.columns, 
            index=X.index
        )
        
        # Z-score标准化
        scaler_zscore = StandardScaler()
        X_zscore = pd.DataFrame(
            scaler_zscore.fit_transform(X), 
            columns=X.columns, 
            index=X.index
        )
        
        print("✓ Min-Max标准化完成")
        print("✓ Z-score标准化完成")
        
        # 保存标准化后的数据（使用Z-score）
        self.X_scaled = X_zscore
        self.y = y
        self.scaler = scaler_zscore
        
        # 标准化效果对比可视化
        self.plot_standardization_comparison(X, X_minmax, X_zscore)
        
        print("✓ 数据预处理完成")
        
    def plot_standardization_comparison(self, X_original, X_minmax, X_zscore):
        """
        标准化效果对比可视化
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('数据标准化效果对比', fontsize=16, fontweight='bold')
        
        # 选择几个代表性特征进行对比
        features_to_plot = ['水泥含量', '水含量', '龄期', '抗压强度']
        
        for i, feature in enumerate(features_to_plot[:3]):
            row = i // 2
            col = i % 2
            
            axes[row, col].hist(X_original[feature], alpha=0.5, label='原始数据', bins=30)
            axes[row, col].hist(X_minmax[feature], alpha=0.5, label='Min-Max标准化', bins=30)
            axes[row, col].hist(X_zscore[feature], alpha=0.5, label='Z-score标准化', bins=30)
            axes[row, col].set_title(f'{feature}标准化对比')
            axes[row, col].legend()
            axes[row, col].grid(True, alpha=0.3)
        
        # 最后一个子图显示目标变量分布
        axes[1, 1].hist(self.y, bins=30, alpha=0.7, color='orange')
        axes[1, 1].set_title('抗压强度分布')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('results/figures/standardization_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✓ 标准化对比图已保存")

    def exploratory_data_analysis(self):
        """
        3. 探索性数据分析与可视化
        """
        print("\n3. 探索性数据分析")
        print("-" * 40)

        # 数据分布可视化
        self.plot_data_distribution()

        # 特征相关性分析
        self.plot_correlation_matrix()

        # 特征与目标变量关系分析
        self.plot_feature_target_relationship()

        print("✓ 探索性数据分析完成")

    def plot_data_distribution(self):
        """绘制数据分布图"""
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        fig.suptitle('混凝土配比特征分布分析', fontsize=16, fontweight='bold')

        columns = self.df.columns
        for i, col in enumerate(columns):
            row = i // 3
            col_idx = i % 3

            # 直方图
            axes[row, col_idx].hist(self.df[col], bins=30, alpha=0.7, edgecolor='black')
            axes[row, col_idx].set_title(f'{col}分布')
            axes[row, col_idx].set_xlabel(col)
            axes[row, col_idx].set_ylabel('频次')
            axes[row, col_idx].grid(True, alpha=0.3)

            # 添加统计信息
            mean_val = self.df[col].mean()
            std_val = self.df[col].std()
            axes[row, col_idx].axvline(mean_val, color='red', linestyle='--',
                                     label=f'均值: {mean_val:.2f}')
            axes[row, col_idx].legend()

        plt.tight_layout()
        plt.savefig('results/figures/data_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 箱线图
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        fig.suptitle('混凝土配比特征箱线图分析', fontsize=16, fontweight='bold')

        for i, col in enumerate(columns):
            row = i // 3
            col_idx = i % 3

            axes[row, col_idx].boxplot(self.df[col])
            axes[row, col_idx].set_title(f'{col}箱线图')
            axes[row, col_idx].set_ylabel(col)
            axes[row, col_idx].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('results/figures/boxplot_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✓ 数据分布图已保存")

    def plot_correlation_matrix(self):
        """绘制特征相关性热力图"""
        plt.figure(figsize=(12, 10))

        # 计算相关性矩阵
        correlation_matrix = self.df.corr()

        # 绘制热力图
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix,
                   mask=mask,
                   annot=True,
                   cmap='RdYlBu_r',
                   center=0,
                   square=True,
                   fmt='.2f',
                   cbar_kws={"shrink": .8})

        plt.title('混凝土配比特征相关性热力图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('results/figures/correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 输出强相关性特征对
        print("强相关性特征对 (|r| > 0.5):")
        strong_corr = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_val = correlation_matrix.iloc[i, j]
                if abs(corr_val) > 0.5:
                    strong_corr.append((
                        correlation_matrix.columns[i],
                        correlation_matrix.columns[j],
                        corr_val
                    ))

        for feat1, feat2, corr in strong_corr:
            print(f"  {feat1} - {feat2}: {corr:.3f}")

        print("✓ 相关性分析完成")

    def plot_feature_target_relationship(self):
        """绘制特征与目标变量关系图"""
        features = [col for col in self.df.columns if col != '抗压强度']

        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        fig.suptitle('特征与抗压强度关系分析', fontsize=16, fontweight='bold')

        for i, feature in enumerate(features):
            row = i // 4
            col = i % 4

            # 散点图
            axes[row, col].scatter(self.df[feature], self.df['抗压强度'],
                                 alpha=0.6, s=20)
            axes[row, col].set_xlabel(feature)
            axes[row, col].set_ylabel('抗压强度 (MPa)')
            axes[row, col].set_title(f'{feature} vs 抗压强度')
            axes[row, col].grid(True, alpha=0.3)

            # 添加趋势线
            z = np.polyfit(self.df[feature], self.df['抗压强度'], 1)
            p = np.poly1d(z)
            axes[row, col].plot(self.df[feature], p(self.df[feature]),
                              "r--", alpha=0.8, linewidth=2)

            # 计算相关系数
            corr = self.df[feature].corr(self.df['抗压强度'])
            axes[row, col].text(0.05, 0.95, f'r = {corr:.3f}',
                              transform=axes[row, col].transAxes,
                              bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

        plt.tight_layout()
        plt.savefig('results/figures/feature_target_relationship.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✓ 特征关系分析完成")

    def feature_engineering_and_split(self):
        """
        4. 特征工程与数据划分
        """
        print("\n4. 特征工程与数据划分")
        print("-" * 40)

        # 数据集划分
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            self.X_scaled, self.y, test_size=0.2, random_state=42
        )

        print(f"✓ 训练集大小: {self.X_train.shape}")
        print(f"✓ 测试集大小: {self.X_test.shape}")

        # PCA降维分析
        self.perform_pca_analysis()

        print("✓ 特征工程完成")

    def perform_pca_analysis(self):
        """PCA主成分分析"""
        print("\nPCA主成分分析:")

        # 执行PCA
        pca_full = PCA()
        pca_full.fit(self.X_train)

        # 计算累积贡献率
        cumsum_ratio = np.cumsum(pca_full.explained_variance_ratio_)

        # 找到解释95%方差的主成分数量
        n_components_95 = np.argmax(cumsum_ratio >= 0.95) + 1
        n_components_90 = np.argmax(cumsum_ratio >= 0.90) + 1

        print(f"  解释90%方差需要: {n_components_90}个主成分")
        print(f"  解释95%方差需要: {n_components_95}个主成分")

        # 可视化PCA结果
        self.plot_pca_analysis(pca_full, cumsum_ratio)

        # 保存最优PCA模型
        self.pca_optimal = PCA(n_components=n_components_95)
        self.X_train_pca = self.pca_optimal.fit_transform(self.X_train)
        self.X_test_pca = self.pca_optimal.transform(self.X_test)

        print(f"✓ PCA降维完成，从{self.X_train.shape[1]}维降至{n_components_95}维")

    def plot_pca_analysis(self, pca, cumsum_ratio):
        """PCA分析可视化"""
        fig, axes = plt.subplots(1, 3, figsize=(18, 5))

        # 1. 主成分贡献率
        axes[0].bar(range(1, len(pca.explained_variance_ratio_) + 1),
                   pca.explained_variance_ratio_)
        axes[0].set_xlabel('主成分')
        axes[0].set_ylabel('方差贡献率')
        axes[0].set_title('各主成分方差贡献率')
        axes[0].grid(True, alpha=0.3)

        # 2. 累积贡献率
        axes[1].plot(range(1, len(cumsum_ratio) + 1), cumsum_ratio, 'bo-')
        axes[1].axhline(y=0.95, color='r', linestyle='--', label='95%')
        axes[1].axhline(y=0.90, color='orange', linestyle='--', label='90%')
        axes[1].set_xlabel('主成分数量')
        axes[1].set_ylabel('累积方差贡献率')
        axes[1].set_title('主成分累积贡献率')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        # 3. 前两个主成分散点图
        pca_2d = PCA(n_components=2)
        X_train_2d = pca_2d.fit_transform(self.X_train)

        scatter = axes[2].scatter(X_train_2d[:, 0], X_train_2d[:, 1],
                                c=self.y_train, cmap='viridis', alpha=0.6)
        axes[2].set_xlabel(f'PC1 ({pca_2d.explained_variance_ratio_[0]:.2%})')
        axes[2].set_ylabel(f'PC2 ({pca_2d.explained_variance_ratio_[1]:.2%})')
        axes[2].set_title('前两个主成分散点图')
        plt.colorbar(scatter, ax=axes[2], label='抗压强度')

        plt.tight_layout()
        plt.savefig('results/figures/pca_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✓ PCA分析图已保存")

    def build_and_train_models(self):
        """
        5. 模型构建与训练
        """
        print("\n5. 模型构建与训练")
        print("-" * 40)

        # 定义模型
        models_config = {
            '线性回归': LinearRegression(),
            '决策树回归': DecisionTreeRegressor(random_state=42),
            '随机森林回归': RandomForestRegressor(random_state=42, n_estimators=100)
        }

        # 训练模型
        for name, model in models_config.items():
            print(f"训练{name}模型...")

            # 使用原始特征训练
            model.fit(self.X_train, self.y_train)
            self.models[name] = model

            # 预测
            y_pred_train = model.predict(self.X_train)
            y_pred_test = model.predict(self.X_test)

            # 计算评估指标
            train_rmse = np.sqrt(mean_squared_error(self.y_train, y_pred_train))
            test_rmse = np.sqrt(mean_squared_error(self.y_test, y_pred_test))
            train_mae = mean_absolute_error(self.y_train, y_pred_train)
            test_mae = mean_absolute_error(self.y_test, y_pred_test)
            train_r2 = r2_score(self.y_train, y_pred_train)
            test_r2 = r2_score(self.y_test, y_pred_test)

            # 保存结果
            self.results[name] = {
                'train_rmse': train_rmse,
                'test_rmse': test_rmse,
                'train_mae': train_mae,
                'test_mae': test_mae,
                'train_r2': train_r2,
                'test_r2': test_r2,
                'y_pred_train': y_pred_train,
                'y_pred_test': y_pred_test
            }

            print(f"  ✓ {name}训练完成")
            print(f"    训练集 RMSE: {train_rmse:.3f}, R²: {train_r2:.3f}")
            print(f"    测试集 RMSE: {test_rmse:.3f}, R²: {test_r2:.3f}")

        # K-Means聚类分析
        self.perform_clustering_analysis()

        print("✓ 所有模型训练完成")

    def perform_clustering_analysis(self):
        """K-Means聚类分析"""
        print("\nK-Means聚类分析:")

        # 确定最优聚类数量
        inertias = []
        silhouette_scores = []
        k_range = range(2, 11)

        for k in k_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(self.X_train)

            inertias.append(kmeans.inertia_)
            silhouette_scores.append(silhouette_score(self.X_train, cluster_labels))

        # 找到最优k值（轮廓系数最大）
        optimal_k = k_range[np.argmax(silhouette_scores)]

        # 使用最优k值进行聚类
        self.kmeans_optimal = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
        train_clusters = self.kmeans_optimal.fit_predict(self.X_train)
        test_clusters = self.kmeans_optimal.predict(self.X_test)

        # 保存聚类结果
        self.results['K-Means聚类'] = {
            'optimal_k': optimal_k,
            'train_clusters': train_clusters,
            'test_clusters': test_clusters,
            'inertia': self.kmeans_optimal.inertia_,
            'silhouette_score': silhouette_score(self.X_train, train_clusters)
        }

        print(f"  ✓ 最优聚类数: {optimal_k}")
        print(f"  ✓ 轮廓系数: {silhouette_score(self.X_train, train_clusters):.3f}")
        print(f"  ✓ 惯性值: {self.kmeans_optimal.inertia_:.3f}")

        # 可视化聚类结果
        self.plot_clustering_analysis(k_range, inertias, silhouette_scores, train_clusters)

    def plot_clustering_analysis(self, k_range, inertias, silhouette_scores, train_clusters):
        """聚类分析可视化"""
        fig, axes = plt.subplots(1, 3, figsize=(18, 5))

        # 1. 肘部法则
        axes[0].plot(k_range, inertias, 'bo-')
        axes[0].set_xlabel('聚类数量 (k)')
        axes[0].set_ylabel('惯性值 (Inertia)')
        axes[0].set_title('肘部法则确定最优聚类数')
        axes[0].grid(True, alpha=0.3)

        # 2. 轮廓系数
        axes[1].plot(k_range, silhouette_scores, 'ro-')
        axes[1].set_xlabel('聚类数量 (k)')
        axes[1].set_ylabel('轮廓系数')
        axes[1].set_title('轮廓系数分析')
        axes[1].grid(True, alpha=0.3)

        # 标记最优k值
        optimal_k = k_range[np.argmax(silhouette_scores)]
        axes[1].axvline(x=optimal_k, color='green', linestyle='--',
                       label=f'最优k={optimal_k}')
        axes[1].legend()

        # 3. 聚类结果可视化（使用前两个主成分）
        pca_2d = PCA(n_components=2)
        X_train_2d = pca_2d.fit_transform(self.X_train)

        scatter = axes[2].scatter(X_train_2d[:, 0], X_train_2d[:, 1],
                                c=train_clusters, cmap='tab10', alpha=0.6)
        axes[2].set_xlabel('第一主成分')
        axes[2].set_ylabel('第二主成分')
        axes[2].set_title(f'K-Means聚类结果 (k={optimal_k})')

        # 添加聚类中心
        centers_2d = pca_2d.transform(self.kmeans_optimal.cluster_centers_)
        axes[2].scatter(centers_2d[:, 0], centers_2d[:, 1],
                       c='red', marker='x', s=200, linewidths=3, label='聚类中心')
        axes[2].legend()

        plt.tight_layout()
        plt.savefig('results/figures/clustering_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✓ 聚类分析图已保存")

    def evaluate_and_compare_models(self):
        """
        6. 模型评估与对比
        """
        print("\n6. 模型评估与对比")
        print("-" * 40)

        # 创建评估结果表格
        self.create_evaluation_table()

        # 绘制模型对比图
        self.plot_model_comparison()

        # 绘制预测结果对比
        self.plot_prediction_comparison()

        # 残差分析
        self.plot_residual_analysis()

        print("✓ 模型评估完成")

    def create_evaluation_table(self):
        """创建模型评估表格"""
        print("模型性能对比表:")
        print("=" * 80)

        # 创建评估数据框
        eval_data = []
        for model_name in ['线性回归', '决策树回归', '随机森林回归']:
            result = self.results[model_name]
            eval_data.append({
                '模型': model_name,
                '训练集RMSE': f"{result['train_rmse']:.3f}",
                '测试集RMSE': f"{result['test_rmse']:.3f}",
                '训练集MAE': f"{result['train_mae']:.3f}",
                '测试集MAE': f"{result['test_mae']:.3f}",
                '训练集R²': f"{result['train_r2']:.3f}",
                '测试集R²': f"{result['test_r2']:.3f}"
            })

        eval_df = pd.DataFrame(eval_data)
        print(eval_df.to_string(index=False))

        # 保存评估结果
        eval_df.to_csv('results/model_evaluation.csv', index=False, encoding='utf-8-sig')

        # 聚类结果
        cluster_result = self.results['K-Means聚类']
        print(f"\nK-Means聚类结果:")
        print(f"最优聚类数: {cluster_result['optimal_k']}")
        print(f"轮廓系数: {cluster_result['silhouette_score']:.3f}")
        print(f"惯性值: {cluster_result['inertia']:.3f}")

        print("✓ 评估表格已保存")

    def plot_model_comparison(self):
        """模型性能对比可视化"""
        models = ['线性回归', '决策树回归', '随机森林回归']

        # 提取评估指标
        train_rmse = [self.results[model]['train_rmse'] for model in models]
        test_rmse = [self.results[model]['test_rmse'] for model in models]
        train_r2 = [self.results[model]['train_r2'] for model in models]
        test_r2 = [self.results[model]['test_r2'] for model in models]

        fig, axes = plt.subplots(1, 2, figsize=(15, 6))

        # RMSE对比
        x = np.arange(len(models))
        width = 0.35

        axes[0].bar(x - width/2, train_rmse, width, label='训练集', alpha=0.8)
        axes[0].bar(x + width/2, test_rmse, width, label='测试集', alpha=0.8)
        axes[0].set_xlabel('模型')
        axes[0].set_ylabel('RMSE')
        axes[0].set_title('模型RMSE对比')
        axes[0].set_xticks(x)
        axes[0].set_xticklabels(models)
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # R²对比
        axes[1].bar(x - width/2, train_r2, width, label='训练集', alpha=0.8)
        axes[1].bar(x + width/2, test_r2, width, label='测试集', alpha=0.8)
        axes[1].set_xlabel('模型')
        axes[1].set_ylabel('R² Score')
        axes[1].set_title('模型R²对比')
        axes[1].set_xticks(x)
        axes[1].set_xticklabels(models)
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('results/figures/model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✓ 模型对比图已保存")

    def plot_prediction_comparison(self):
        """预测结果对比可视化"""
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        models = ['线性回归', '决策树回归', '随机森林回归']

        for i, model_name in enumerate(models):
            y_pred_test = self.results[model_name]['y_pred_test']
            r2 = self.results[model_name]['test_r2']

            # 真实值 vs 预测值散点图
            axes[i].scatter(self.y_test, y_pred_test, alpha=0.6, s=20)

            # 添加理想预测线
            min_val = min(self.y_test.min(), y_pred_test.min())
            max_val = max(self.y_test.max(), y_pred_test.max())
            axes[i].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)

            axes[i].set_xlabel('真实抗压强度 (MPa)')
            axes[i].set_ylabel('预测抗压强度 (MPa)')
            axes[i].set_title(f'{model_name}\nR² = {r2:.3f}')
            axes[i].grid(True, alpha=0.3)

            # 添加统计信息
            rmse = self.results[model_name]['test_rmse']
            mae = self.results[model_name]['test_mae']
            axes[i].text(0.05, 0.95, f'RMSE: {rmse:.2f}\nMAE: {mae:.2f}',
                        transform=axes[i].transAxes,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                        verticalalignment='top')

        plt.tight_layout()
        plt.savefig('results/figures/prediction_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✓ 预测对比图已保存")

    def plot_residual_analysis(self):
        """残差分析"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        models = ['线性回归', '决策树回归', '随机森林回归']

        for i, model_name in enumerate(models):
            y_pred_test = self.results[model_name]['y_pred_test']
            residuals = self.y_test - y_pred_test

            # 残差散点图
            axes[0, i].scatter(y_pred_test, residuals, alpha=0.6, s=20)
            axes[0, i].axhline(y=0, color='r', linestyle='--')
            axes[0, i].set_xlabel('预测值')
            axes[0, i].set_ylabel('残差')
            axes[0, i].set_title(f'{model_name} - 残差散点图')
            axes[0, i].grid(True, alpha=0.3)

            # 残差直方图
            axes[1, i].hist(residuals, bins=30, alpha=0.7, edgecolor='black')
            axes[1, i].set_xlabel('残差')
            axes[1, i].set_ylabel('频次')
            axes[1, i].set_title(f'{model_name} - 残差分布')
            axes[1, i].grid(True, alpha=0.3)

            # 添加正态性检验
            _, p_value = stats.normaltest(residuals)
            axes[1, i].text(0.05, 0.95, f'正态性检验\np-value: {p_value:.4f}',
                           transform=axes[1, i].transAxes,
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                           verticalalignment='top')

        plt.tight_layout()
        plt.savefig('results/figures/residual_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✓ 残差分析图已保存")

    def generate_interactive_charts(self):
        """
        7. 生成交互式图表（可选）
        """
        print("\n7. 生成交互式图表")
        print("-" * 40)

        try:
            from pyecharts import options as opts
            from pyecharts.charts import Scatter, Bar, Line
            from pyecharts.globals import ThemeType

            # 交互式散点图：特征与目标变量关系
            scatter = (
                Scatter(init_opts=opts.InitOpts(theme=ThemeType.MACARONS, width="1200px", height="600px"))
                .add_xaxis(self.df['水泥含量'].tolist())
                .add_yaxis("抗压强度",
                          list(zip(self.df['水泥含量'].tolist(), self.df['抗压强度'].tolist())),
                          symbol_size=8)
                .set_global_opts(
                    title_opts=opts.TitleOpts(title="水泥含量与抗压强度关系"),
                    xaxis_opts=opts.AxisOpts(name="水泥含量 (kg/m³)"),
                    yaxis_opts=opts.AxisOpts(name="抗压强度 (MPa)"),
                    tooltip_opts=opts.TooltipOpts(is_show=True),
                    datazoom_opts=[opts.DataZoomOpts(type_="slider")]
                )
            )
            scatter.render("results/figures/interactive_scatter.html")

            # 交互式柱状图：模型性能对比
            models = ['线性回归', '决策树回归', '随机森林回归']
            test_r2 = [self.results[model]['test_r2'] for model in models]

            bar = (
                Bar(init_opts=opts.InitOpts(theme=ThemeType.MACARONS))
                .add_xaxis(models)
                .add_yaxis("R² Score", test_r2)
                .set_global_opts(
                    title_opts=opts.TitleOpts(title="模型性能对比"),
                    yaxis_opts=opts.AxisOpts(name="R² Score"),
                    tooltip_opts=opts.TooltipOpts(is_show=True)
                )
            )
            bar.render("results/figures/interactive_model_comparison.html")

            print("✓ 交互式图表已生成")
            print("  - interactive_scatter.html")
            print("  - interactive_model_comparison.html")

        except ImportError:
            print("⚠ pyecharts未安装，跳过交互式图表生成")
        except Exception as e:
            print(f"⚠ 交互式图表生成失败: {e}")

    def generate_summary_report(self):
        """
        8. 生成项目总结报告
        """
        print("\n8. 项目总结报告")
        print("=" * 60)

        # 找到最佳模型
        best_model_name = max(self.results.keys(),
                             key=lambda x: self.results[x]['test_r2'] if 'test_r2' in self.results[x] else 0)
        best_result = self.results[best_model_name]

        report = f"""
混凝土抗压强度预测项目分析报告
{'='*50}

1. 数据集概况
   - 样本数量: {self.df.shape[0]}
   - 特征数量: {self.df.shape[1] - 1}
   - 目标变量: 抗压强度 (MPa)
   - 数据质量: 无缺失值，数据完整

2. 数据特征分析
   - 抗压强度范围: {self.df['抗压强度'].min():.2f} - {self.df['抗压强度'].max():.2f} MPa
   - 平均抗压强度: {self.df['抗压强度'].mean():.2f} MPa
   - 标准差: {self.df['抗压强度'].std():.2f} MPa

3. 模型性能对比
   最佳模型: {best_model_name}
   - 测试集R²: {best_result['test_r2']:.3f}
   - 测试集RMSE: {best_result['test_rmse']:.3f} MPa
   - 测试集MAE: {best_result['test_mae']:.3f} MPa

4. 所有模型性能:"""

        for model_name in ['线性回归', '决策树回归', '随机森林回归']:
            result = self.results[model_name]
            report += f"""
   {model_name}:
     - 测试集R²: {result['test_r2']:.3f}
     - 测试集RMSE: {result['test_rmse']:.3f} MPa"""

        cluster_result = self.results['K-Means聚类']
        report += f"""

5. 聚类分析结果
   - 最优聚类数: {cluster_result['optimal_k']}
   - 轮廓系数: {cluster_result['silhouette_score']:.3f}

6. 主要发现与建议
   - 随机森林模型通常表现最佳，具有良好的泛化能力
   - 水泥含量和龄期是影响抗压强度的重要因素
   - 建议在实际应用中使用集成学习方法提高预测精度
   - 可以考虑特征工程进一步优化模型性能

7. 文件输出
   - 模型评估结果: results/model_evaluation.csv
   - 可视化图表: results/figures/目录下
   - 交互式图表: results/figures/interactive_*.html
"""

        print(report)

        # 保存报告
        with open('results/analysis_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

        print("✓ 分析报告已保存至 results/analysis_report.txt")

    def run_complete_analysis(self):
        """运行完整的分析流程"""
        try:
            # 1. 数据加载与探索
            if not self.load_and_explore_data():
                return False

            # 2. 数据预处理
            self.data_preprocessing()

            # 3. 探索性数据分析
            self.exploratory_data_analysis()

            # 4. 特征工程与数据划分
            self.feature_engineering_and_split()

            # 5. 模型构建与训练
            self.build_and_train_models()

            # 6. 模型评估与对比
            self.evaluate_and_compare_models()

            # 7. 生成交互式图表
            self.generate_interactive_charts()

            # 8. 生成总结报告
            self.generate_summary_report()

            print("\n" + "="*60)
            print("🎉 混凝土抗压强度预测项目分析完成！")
            print("📁 所有结果已保存至 results/ 目录")
            print("="*60)

            return True

        except Exception as e:
            print(f"\n❌ 分析过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🚀 启动混凝土抗压强度预测项目")

    # 创建分析实例
    analyzer = ConcreteStrengthAnalysis('concrete.csv')

    # 运行完整分析
    success = analyzer.run_complete_analysis()

    if success:
        print("\n✅ 项目执行成功！")
        print("📊 请查看 results/ 目录下的分析结果")
    else:
        print("\n❌ 项目执行失败，请检查错误信息")


if __name__ == "__main__":
    main()
