#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
混凝土抗压强度预测项目测试脚本
用于验证代码是否能正常运行
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score

# 忽略警告
warnings.filterwarnings('ignore')

def quick_test():
    """快速测试分析流程"""
    print("🧪 开始快速测试...")
    
    try:
        # 1. 加载数据
        print("1. 加载数据...")
        # 尝试不同编码
        try:
            df = pd.read_csv('concrete.csv', encoding='utf-8')
        except UnicodeDecodeError:
            try:
                df = pd.read_csv('concrete.csv', encoding='latin-1')
            except UnicodeDecodeError:
                df = pd.read_csv('concrete.csv', encoding='gbk')
        
        # 设置列名
        column_names = [
            '水泥含量', '高炉矿渣', '粉煤灰', '水含量', 
            '高效减水剂', '粗骨料', '细骨料', '龄期', '抗压强度'
        ]
        df.columns = column_names
        
        print(f"✓ 数据加载成功: {df.shape}")
        
        # 2. 基本统计
        print("2. 基本统计...")
        print(f"✓ 数据形状: {df.shape}")
        print(f"✓ 缺失值: {df.isnull().sum().sum()}")
        print(f"✓ 抗压强度范围: {df['抗压强度'].min():.2f} - {df['抗压强度'].max():.2f}")
        
        # 3. 数据预处理
        print("3. 数据预处理...")
        X = df.drop('抗压强度', axis=1)
        y = df['抗压强度']
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )
        print(f"✓ 训练集: {X_train.shape}, 测试集: {X_test.shape}")
        
        # 4. 模型训练
        print("4. 模型训练...")
        model = LinearRegression()
        model.fit(X_train, y_train)
        
        # 预测
        y_pred = model.predict(X_test)
        
        # 评估
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        print(f"✓ 线性回归模型:")
        print(f"  RMSE: {rmse:.3f}")
        print(f"  R²: {r2:.3f}")
        
        # 5. 简单可视化
        print("5. 生成测试图表...")
        plt.figure(figsize=(10, 4))
        
        # 数据分布
        plt.subplot(1, 2, 1)
        plt.hist(df['抗压强度'], bins=30, alpha=0.7)
        plt.title('抗压强度分布')
        plt.xlabel('抗压强度 (MPa)')
        plt.ylabel('频次')
        
        # 预测结果
        plt.subplot(1, 2, 2)
        plt.scatter(y_test, y_pred, alpha=0.6)
        plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
        plt.xlabel('真实值')
        plt.ylabel('预测值')
        plt.title(f'预测结果 (R²={r2:.3f})')
        
        plt.tight_layout()
        plt.savefig('test_result.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✅ 快速测试完成！")
        print("📊 测试图表已保存为 test_result.png")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_test()
